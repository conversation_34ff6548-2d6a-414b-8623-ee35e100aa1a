{"name": "free-nextjs-admin-dashboard", "version": "2.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "@tanstack/query-core": "^5.80.6", "@tanstack/react-query": "^5.80.6", "@tippyjs/react": "^4.2.6", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "flatpickr": "^4.6.13", "flowbite": "^3.1.2", "framer-motion": "^12.23.0", "js-cookie": "^3.0.5", "ky": "^1.8.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "^15.1.3", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-flatpickr": "^3.10.13", "react-icons": "^5.5.0", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "recharts": "^3.0.2", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-flatpickr": "^3.8.11", "@types/react-transition-group": "^4.4.12", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "babel-loader": "^10.0.0", "eslint": "^9.23.0", "eslint-config-next": "15.1.3", "eslint-config-prettier": "^10.1.1", "file-loader": "^6.2.0", "postcss": "^8", "tailwindcss": "^4.0.0", "typescript": "^5", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}}