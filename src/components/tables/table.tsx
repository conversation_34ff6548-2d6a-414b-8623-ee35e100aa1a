"use client";
import { useState } from "react";
import { IoIosSearch } from "react-icons/io";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { FiFilter, FiDownload, FiMoreVertical,FiTrash2 } from "react-icons/fi";
import { HiPlus } from "react-icons/hi";
import AddAdminModal from "@/components/modal/new-admin";

export default function Adminpage({ headers = [], data = [], actions }) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const accessSections = [
    { title: "Dispatch Control", items: ["View", "Edit", "Delete"] },
    { title: "Financial Management", items: ["View Reports", "Edit Budgets"] },
  ];
  return (


 <div>
    <div className="flex justify-end">
              <button className="flex items-center gap-2 px-4 py-2 bg-[#3324E3] text-white rounded-full text-xs sm:text-sm font-medium">
                <HiPlus /> New Admin 
              </button>
    </div>
    <div className="mt-6 overflow-visible rounded-xl border border-gray-200 bg-white">
                <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-[12px]">
                    <form className="flex-1 max-w-md">
                        <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
                        Search
                        </label>
                        <div className="relative">
                        <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <IoIosSearch size={20}/>
                        </div>
                        <input
                            id="default-search"
                            type="search"
                            placeholder="Search here"
                            className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
                        />
                        </div>
                    </form>
                    <div className="flex items-center gap-2">
                            <button
                                type="button"
                                className="flex items-center gap-1.5 py-2 px-3 text-xs font-medium text-[#13BB76] bg-white rounded-full border border-gray-200 hover:bg-gray-100 focus:outline-none"
                                >
                                <span className="w-2 h-2 rounded-full bg-[#13BB76]"></span>
                                Active
                            </button>
                    
                        {/* Inactive Button */}
                            <button
                            type="button"
                            className="flex items-center gap-1.5 py-2 px-3 text-xs font-medium text-[#76787A] bg-white rounded-full 
                            border border-gray-200 hover:bg-gray-100 focus:outline-none">
                            <span className="w-2 h-2 rounded-full bg-[#76787A]"></span>
                            Inactive
                            </button>
                        <div className="relative inline-block text-left" data-headlessui-state="">
                            <button
                                id="headlessui-menu-button-r2"
                                type="button"
                                aria-haspopup="menu"
                                aria-expanded="false"
                                data-headlessui-state=""
                                className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                            >
                            <FiFilter size={18}/>
                                Filters
                            </button>
                        </div>
                    
                            <button
                            type="button"
                            aria-label="clock"
                            className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white 
                            rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 
                            focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 
                            dark:hover:text-white dark:hover:bg-gray-700">
                                <RxCounterClockwiseClock size={22} />
                            </button>
                        
                            <button
                            aria-label="download"
                            type="button"
                            className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border 
                            border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 
                            dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white 
                            dark:hover:bg-gray-700">
                            <FiDownload size={16}/>
                            </button>
                    </div>
                </div>
    <div className="max-w-full overflow-x-auto custom-scrollbar">
        <div className="max-w-[992px] min-w-[-webkit-fill-available]">
       <table className="min-w-full  undefined text-[11px] text-left table-auto">
        <thead className="text-[#76787A] border-b">
          <tr>
            {headers.map((header, index) => (
              <th
                key={index}
                className="px-4 py-3 font-medium text-nowrap"
              >
                {header}
              </th>
            ))}
            {actions && <th className="px-4 py-3 font-medium text-nowrap">Actions</th>}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100">
          {data.length > 0 ? (
            data.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-[#E4FFF4]">
                {headers.map((header, colIndex) => (
                  <td
                    key={colIndex}
                    className="px-4 py-3 text-[#050013]"
                  >
                    {row[header]}
                  </td>
                ))}
                {actions && (
                  <td className="px-4 py-3 text-[#050013]">{actions(row)}</td>
                )}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={headers.length + (actions ? 1 : 0)}
                className="px-4 py-3 text-[#050013] text-nowrap"
              >
                No data available
              </td>
            </tr>
          )}
        </tbody>
      </table>
      </div>
    </div>
    </div>
    </div>
  );
}
