"use client";
import { memo, useState } from "react";
import { FaChevronDown, FaCheck } from "react-icons/fa6";

function AccordionSection({ title, items, onPress }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4">
      <div
        className="flex cursor-pointer items-center justify-between pb-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <label className="flex cursor-pointer items-center gap-3">
          <input type="checkbox" className="peer hidden" />
          <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]">
            <FaCheck className="text-[#18EC94] peer-checked:text-[#3324E3]" />
          </span>
          <span className="text-[14px] font-medium text-[#050013] peer-checked:text-[#050013]">
            {title}
          </span>
        </label>
        <span className="bg-tables rounded-full p-1">
          <FaChevronDown
            className={`h-3 w-3 text-gray-500 transition-transform duration-200 ${
              isOpen ? "rotate-180" : "rotate-0"
            }`}
          />
        </span>
      </div>

      {isOpen && (
        <div className="mt-2 mb-6">
          {items?.map((item, idx) => (
            <label
              key={idx}
              className="mb-2 flex cursor-pointer items-center space-x-2"
              onClick={() => onPress(item.id)}
            >
              <input type="checkbox" className="peer hidden" />
              <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]">
                <FaCheck className="text-[#18EC94] peer-checked:text-[#3324E3]" />
              </span>
              <span className="text-sm text-[#76787A] peer-checked:text-[#050013]">
                {item?.description}
              </span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}
export default memo(AccordionSection);
