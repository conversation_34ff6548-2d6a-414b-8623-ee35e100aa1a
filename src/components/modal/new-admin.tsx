"use client";
import React, { useRef, useEffect } from "react";

export default function AddAdminModal({
  isOpen,
  onClose,
  accessSections = [],
}) {
  const modalRef = useRef(null);

  // Close modal on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-999 flex custom-scrollbar">
      <div className="absolute inset-0 bg-black/30"></div>
      <div
        ref={modalRef}
        className="ml-auto w-full max-w-md bg-white h-full shadow-xl transform translate-x-0 transition-transform duration-300 ease-out rounded-tl-[30px] rounded-bl-[30px] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-4 px-6 py-5 bg-tables">
          <h2 className="text-[20px] font-normal text-[#050013]">
            New Super Admins
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500"
          >
            ✕
          </button>
        </div>

        {/* Form */}
        <form className="flex flex-col gap-6">
          {/* Basic Information */}
          <div className="px-6 py-3">
            <h3 className="text-[14px] font-medium text-[#050013] mb-2">
              Basic Information
            </h3>
            <input
              type="text"
              placeholder="Full Name"
              className="w-full p-2 mb-3 border rounded-md focus:ring-2 focus:ring-blue-500 outline-none text-[13px] text-[#76787A]"
            />
            <input
              type="email"
              placeholder="Email ID"
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 outline-none text-[13px] text-[#76787A]"
            />
          </div>

          {/* Access Control Sections */}
          <div className="px-6 py-3">
            <h3 className="text-[14px] font-medium text-[#050013] mb-4">
              Access Control and Permissions
            </h3>
            {accessSections.map((section, idx) => (
              <AccordionSection
                key={idx}
                title={section.title}
                items={section.items}
              />
            ))}
          </div>

          {/* Footer */}
          <div className="sticky bottom-0 border-t bg-white px-6 py-3">
            <button
              type="submit"
              className="bg-[#3707EF] text-white py-2 px-6 rounded-full hover:bg-[#3d0cc0] float-right"
            >
              Add
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Dummy AccordionSection placeholder
function AccordionSection({ title, items }) {
  return (
    <div className="mb-2 border rounded p-2">
      <h4 className="font-medium text-sm">{title}</h4>
      <ul className="text-xs text-gray-600 pl-4 list-disc">
        {items.map((item, idx) => (
          <li key={idx}>{item}</li>
        ))}
      </ul>
    </div>
  );
}
