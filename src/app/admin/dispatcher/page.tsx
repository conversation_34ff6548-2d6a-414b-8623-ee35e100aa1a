"use client";

import React, { useState, useRef, useEffect } from "react";
import { Lu<PERSON><PERSON>cil } from "react-icons/lu";
import { FaChevronDown, FaCheck } from "react-icons/fa6";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { FiFilter, FiDownload, FiMoreVertical,FiTrash2 } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { HiPlus } from "react-icons/hi";
import Link from "next/link";

function AccordionSection({ title, items }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4">
      <div
        className="flex justify-between items-center cursor-pointer pb-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <label className="gap-3 flex items-center cursor-pointer">
        <div className="relative flex items-center">
             <input type="checkbox" className="peer hidden" />
              <span className="h-4 w-4 border rounded-sm border-gray-400 peer-checked:bg-[#3324E3] peer-checked:border-[#3324E3] flex items-center justify-center text-white text-xs font-bold"> </span>
              <FaCheck size={12} className="absolute left-[2px] peer-[&:not(:checked)]:opacity-0 peer-checked:opacity-100 transition-opacity duration-200 peer-checked:text-[#18EC94]"/>
              </div>
          <span className="text-[14px] font-medium text-[#050013] peer-checked:text-[#050013]">
            {title}
          </span>
        </label>
        <span className="bg-tables p-1 rounded-full
        ">
        <FaChevronDown
          className={`w-3 h-3 text-gray-500 transition-transform duration-200 ${
            isOpen ? "rotate-180" : "rotate-0"
          }`}
        />
        </span>
      </div>

      {isOpen && (
        <div className="mt-2 mb-6">
          {items.map((item, idx) => (
            <label
              key={idx}
              className="flex items-center space-x-2 mb-2 cursor-pointer"
            >
              <div className="relative flex items-center">
             <input type="checkbox" className="peer hidden" />
              <span className="h-4 w-4 border rounded-sm border-gray-400 peer-checked:bg-[#3324E3] peer-checked:border-[#3324E3] flex items-center justify-center text-white text-xs font-bold"> </span>
              <FaCheck size={12} className="absolute left-[2px] peer-[&:not(:checked)]:opacity-0 peer-checked:opacity-100 transition-opacity duration-200 peer-checked:text-[#18EC94]"/>
              </div>
              <span className="text-sm peer-checked:text-[#050013] text-[#76787A]">
                {item}
              </span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}

export default function dispatcher() {
  const [activeTab, setActiveTab] = useState("Super Admins");
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [editUser, setEditUser] = useState(null);
  const modalRef = useRef(null);

  const superAdmins = [
    { date: "11-01-2024", name: "John", status: "Active" },
    { date: "11-01-2024", name: "Dave", status: "Active" },
    { date: "11-01-2024", name: "Monty", status: "Active" },
    { date: "26-12-2024", name: "Sam", status: "Inactive" },
  ];

  const clients = [
    { icon: "SL", date: "10-02-2024", name: "Silverline Solutions", status: "Active" },
    { icon: "FE", date: "12-02-2024", name: "FleetEdge", status: "Inactive" },
  ];

  const currentData = activeTab === "Super Admins" ? superAdmins : clients;
  const filteredData = currentData.filter((row) =>
    row.name.toLowerCase().includes(search.toLowerCase())
  );

  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsModalOpen(false);
      }
    }
    if (isModalOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isModalOpen]);

  const accessSections = [
    {
      title: "User & Role Management",
      items: [
        "Create, edit, delete , Dispatchers, Drivers, Users",
        "Assign Admins to taxi companies",
        "Manage permissions for all roles",
        "Suspend/reactivate any user",
      ],
    },
    {
      title: "Booking & Dispatch Control",
      items: [
        "View, edit, and manage all bookings",
        "Assign/reassign drivers to rides",
        "View driver live locations",
        "Cancel or reschedule any ride",
        "View driver live locations",
      ],
    },
    {
      title: "Company & Financial Management",
      items: [
        "Add, edit, or remove taxi companies",
        "Manage company subscription plans",
        "View & modify company-specific pricing and fare structures",
        "Access & edit company billing information",
      ],
    },
    {
      title: "System & Policy Settings",
      items: [
        "Define platform-wide fare policies",
        "Set geofencing rules & restrictions",
        "Control global discount and promo policies",
        "Configure ride cancellation policies",
      ],
    },
    {
      title: "Reporting & Analytics",
      items: [
        "View and export reports on revenue, ride activity, and system performance",
        "Monitor driver performance & customer ratings",
        "Analyze dispatcher efficiency",
      ],
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-end items-center">
        <button onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-[#3324E3] text-white rounded-full text-xs sm:text-sm font-medium">
            <HiPlus /> New Dispatcher
        </button>
      </div>

      {/* Table */}
      <div className="mt-6 overflow-visible rounded-xl border border-gray-200 bg-white">
      <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-[12px]">
            <form className="flex-1 max-w-md">
                <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
                Search
                </label>
                <div className="relative">
                <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
            <IoIosSearch size={20}/>
                </div>
                <input
                    id="default-search"
                    type="search"
                    placeholder="Search here"
                    className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
                />
                </div>
            </form>

  <div className="flex items-center gap-2">
  <button
      type="button"
      className="flex items-center gap-1.5 py-2 px-3 text-xs font-medium text-[#13BB76] bg-white rounded-full border border-gray-200 hover:bg-gray-100 focus:outline-none"
    >
      <span className="w-2 h-2 rounded-full bg-[#13BB76]"></span>
      Active
    </button>

    {/* Inactive Button */}
    <button
      type="button"
      className="flex items-center gap-1.5 py-2 px-3 text-xs font-medium text-[#76787A] bg-white rounded-full border border-gray-200 hover:bg-gray-100 focus:outline-none"
    >
      <span className="w-2 h-2 rounded-full bg-[#76787A]"></span>
      Inactive
    </button>
    <div className="relative inline-block text-left" data-headlessui-state="">
      <button
        id="headlessui-menu-button-r2"
        type="button"
        aria-haspopup="menu"
        aria-expanded="false"
        data-headlessui-state=""
        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      >
       <FiFilter size={18}/>
        Filters
      </button>
    </div>

    <button
      type="button"
      aria-label="clock"
      className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
    >
<RxCounterClockwiseClock size={22} />

    </button>

    <button
      aria-label="download"
      type="button"
      className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
    >
       <FiDownload size={16}/>
    </button>
  </div>
</div>
<div className="max-w-full overflow-x-auto custom-scrollbar">
        <div className="max-w-[992px] min-w-[-webkit-fill-available]">
          <table className="min-w-full  undefined text-[11px] text-left table-auto">
          <thead className="text-[#76787A]">
 
    <tr>
      <th className="px-4 py-3 font-medium">Date</th>
      <th className="px-4 py-3 font-medium">User Name</th>
      <th className="px-4 py-3 font-medium">Role Name</th>
      <th className="px-4 py-3 font-medium">Dispatch Control</th>
      <th className="px-4 py-3 font-medium">Financial Management</th>
      <th className="px-4 py-3 font-medium">Global Policies</th>
      <th className="px-4 py-3 font-medium">Status</th>
      <th className="px-4 py-3"></th>
    </tr>
</thead>

<tbody className="divide-y divide-gray-100">
  {filteredData.map((row, index) => (
    <tr key={index} className="hover:bg-[#E4FFF4]">
      <td className="px-4 py-3 text-[#050013]">{row.date}</td>
      <td
        className="px-4 py-3 text-[#050013] font-medium cursor-pointer"
      >
        {row.name}
      </td>
      <td className="px-4 py-3 text-[#050013]">Super Admin</td>
      <td className="px-4 py-3 text-[#050013]">Full Access</td>
      <td className="px-4 py-3 text-[#050013]">Full Access</td>
      <td className="px-4 py-3 text-[#050013]">Full Access</td>
      <td className="px-4 py-3">
        <span
          className={`flex items-center gap-1 ${
            row.status === "Active" ? "text-[#13BB76]" : "text-[#8C8B9F]"
          }`}
        >
          <span className="w-[6px] h-[6px] rounded-full bg-current"></span>
          {row.status}
        </span>
      </td>
      <td className="px-4 py-3">
        <div className="flex justify-center gap-3">
          <LuPencil
            className="w-4 h-4 text-[#050013] cursor-pointer"
          />
          <FiTrash2 className="w-4 h-4 text-[#050013] cursor-pointer" />
        </div>
      </td>
    </tr>
  ))}
</tbody>


          </table>
        </div>
        </div>
      </div>

      {/* add new super admin modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-999 flex custom-scrollbar">
          <div className="absolute inset-0 bg-black/30"></div>
          <div
            ref={modalRef}
            className="ml-auto w-full max-w-md bg-white h-full shadow-xl transform translate-x-0 transition-transform duration-300 ease-out rounded-tl-[30px] rounded-bl-[30px] overflow-y-auto"
          >
            <div className="flex justify-between items-center mb-4 px-6 py-5 bg-tables">
              <h2 className="text-[20px] font-normal text-[#050013]">
              New Dispatcher
              </h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <form className="flex flex-col gap-6">
              <div className="px-6 py-3">
                <h3 className="text-[14px] font-medium text-[#050013] mb-2">
                  Basic Information
                </h3>
                <input
                  type="text"
                  placeholder="Full Name"
                  className="w-full p-2 mb-3 border rounded-md focus:ring-2 focus:ring-blue-500 outline-none text-[13px] text-[#76787A]"
                />
                <input
                  type="email"
                  placeholder="Email ID"
                  className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 outline-none text-[13px] text-[#76787A]"
                />
              </div>

              <div className="px-6 py-3">
                <h3 className="text-[14px] font-medium text-[#050013] mb-4">
                  Access Control and Permissions
                </h3>
                {accessSections.map((section, idx) => (
                  <AccordionSection key={idx} title={section.title} items={section.items} />
                ))}
              </div>

              <div className="sticky bottom-0 border-t bg-white px-6 py-3">
  <button
    type="submit"
    className="bg-[#3707EF] text-white py-2 px-6 rounded-full hover:bg-[#3d0cc0] float-right"
  >
    Add
  </button>
</div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
