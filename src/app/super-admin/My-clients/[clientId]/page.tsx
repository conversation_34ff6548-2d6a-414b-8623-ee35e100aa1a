"use client";

import React, { useState } from "react";
import Link from "next/link";
import { FiEdit } from "react-icons/fi";
import { GoDownload } from "react-icons/go";
import Image from "next/image";
import { ClientType } from "@/utils/types";
import { backendApiClient } from "@/utils/apiClient";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";

/*
================================================================================
| 1. TYPE DEFINITIONS                                                        |
================================================================================
*/

interface OverviewCardProps {
  icon: React.ReactNode;
  value: string;
  subheading: string;
  linkText: string;
  href: string;
}

type ClientResponse = {
  success: boolean;
  data: ClientType;
  meta: unknown;
};

/*
================================================================================
| 2. REUSABLE SUB-COMPONENTS                                                 |
================================================================================
*/

function OverviewCard({
  icon,
  value,
  subheading,
  linkText,
  href,
}: OverviewCardProps) {
  return (
    <div className="flex flex-col justify-between rounded-lg border border-gray-200 bg-white">
      <div className="flex items-center justify-between p-4">
        <div className="space-y-1">
          <p className="text-sm text-gray-500">{subheading}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
          {icon}
        </div>
      </div>
      <div className="rounded-b-lg bg-gray-50 px-4 py-3">
        <p className="text-sm font-medium text-[#3324E3] hover:underline">
          {linkText}
        </p>
      </div>
    </div>
  );
}

function InfoBlock({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) {
  return (
    <div className="flex flex-col">
      <span className="text-xs font-normal text-gray-500">{label}</span>
      <span className="text-sm font-medium text-gray-800">
        {value || "---"}
      </span>
    </div>
  );
}

/*
================================================================================
| 3. MAIN LAYOUT COMPONENTS                                                  |
================================================================================
*/

function ClientHeader({
  client,
  onViewMoreClick,
}: {
  client: ClientType;
  onViewMoreClick: () => void;
}) {
  const { push } = useRouter();
  return (
    <div className="flex flex-col justify-between gap-6 rounded-t-lg border-b bg-white p-6 md:flex-row md:items-start">
      <div className="flex flex-shrink-0 items-center gap-4">
        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
          <Image
            src={client.logoUrl || "/images/name-logo.svg"}
            alt="Company Logo"
            width={80}
            height={80}
            objectFit="fill"
            loading="lazy"
          />
        </div>
        <div>
          <h2 className="text-2xl font-medium text-gray-900">
            {client.companyName}
          </h2>
          <div
            className={`mt-1 flex items-center gap-2 text-xs font-medium ${client.status === "active" ? "text-green-600" : "text-yellow-600"}`}
          >
            <span
              className={`h-2 w-2 rounded-full ${client.status === "active" ? "bg-green-500" : "bg-yellow-500"}`}
            ></span>
            <span className="capitalize">{client.status}</span>
          </div>
        </div>
      </div>

      <div className="flex flex-1 flex-col gap-4 border-t pt-4 md:flex-row md:gap-12 md:border-t-0 md:border-l md:pt-0 md:pl-6">
        <div className="space-y-3">
          <InfoBlock label="Email Id" value={client.primaryContactEmail} />
          <InfoBlock label="Phone no" value={client.primaryContactPhone} />
          <InfoBlock label="Subscription Plan" value="Basic" />
        </div>
        <div className="space-y-3">
          <InfoBlock
            label="Primary Contact Name"
            value={client.primaryContactName}
          />
          <InfoBlock label="Address" value={client.headOfficeAddress} />
          <button
            onClick={onViewMoreClick}
            className="text-sm text-[#3324E3] underline"
          >
            View More
          </button>
        </div>
      </div>

      <div className="flex items-start gap-1">
        <button
          className="rounded-md p-2 text-gray-500 hover:bg-gray-100"
          title="Edit"
          onClick={() => push(`edit/${client.id}`)}
        >
          <FiEdit size={18} />
        </button>
        <button
          className="rounded-md p-2 text-gray-500 hover:bg-gray-100"
          title="Download"
        >
          <GoDownload size={20} />
        </button>
      </div>
    </div>
  );
}

function OverviewGrid({ client }: { client: ClientType }) {
  const cardData = [
    {
      subheading: "Finances",
      value: "250/500",
      icon: (
        <Image src="/images/finance.svg" alt="Finance" width={24} height={24} />
      ),
      linkText: "View Invoices",
      href: "/super-admin/finance",
    },
    {
      subheading: "Members",
      value: "02",
      icon: (
        <Image src="/images/members.svg" alt="Members" width={24} height={24} />
      ),
      linkText: "View All",
      href: "/super-admin/members",
    },
    {
      subheading: "Vehicles",
      value: "17",
      icon: (
        <Image
          src="/images/vehicle.svg"
          alt="Vehicles"
          width={24}
          height={24}
        />
      ),
      linkText: "View All",
      href: "/super-admin/vehicles",
    },
    {
      subheading: "Drivers",
      value: "17",
      icon: (
        <Image src="/images/driver.svg" alt="Drivers" width={24} height={24} />
      ),
      linkText: "View All",
      href: "/super-admin/drivers",
    },
    {
      subheading: "Fleet Size",
      value: client.fleetSize.toString(),
      icon: (
        <Image src="/images/client.svg" alt="Clients" width={24} height={24} />
      ),
      linkText: "View All",
      href: "/super-admin/clients",
    },
    {
      subheading: "Passengers",
      value: "750",
      icon: (
        <Image
          src="/images/passanger.png"
          alt="Passenger"
          width={24}
          height={24}
        />
      ),
      linkText: "View All",
      href: "#",
    },
  ];
  return (
    <div className="rounded-b-lg bg-gray-50 p-6">
      <h3 className="mb-4 text-sm font-medium text-gray-500">Overview</h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {cardData.map((card) => (
          <OverviewCard key={card.subheading} {...card} />
        ))}
      </div>
    </div>
  );
}

function DetailsModal({
  user,
  onClose,
}: {
  user: ClientType | null;
  onClose: () => void;
}) {
  const [activeTab, setActiveTab] = useState("general");

  if (!user) return null;

  const accessSections = user.permissions || []; // Use permissions from the user object

  return (
    <div className="fixed inset-0 z-50 flex justify-end">
      <div className="absolute inset-0 bg-black/40" onClick={onClose}></div>
      <div className="relative flex h-full w-full max-w-lg flex-col bg-white shadow-xl">
        <div className="flex items-center justify-between border-b p-6">
          <h2 className="text-lg font-medium text-gray-900">View Details</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-800"
          >
            ✕
          </button>
        </div>

        <div className="border-b">
          <div className="flex gap-6 px-6">
            <button
              onClick={() => setActiveTab("general")}
              className={`py-3 text-sm ${activeTab === "general" ? "border-b-2 border-[#3324E3] font-semibold text-[#3324E3]" : "text-gray-500"}`}
            >
              General Info
            </button>
            <button
              onClick={() => setActiveTab("permissions")}
              className={`py-3 text-sm ${activeTab === "permissions" ? "border-b-2 border-[#3324E3] font-semibold text-[#3324E3]" : "text-gray-500"}`}
            >
              Permissions
            </button>
          </div>
        </div>

        <div className="flex-grow overflow-y-auto p-6">
          {activeTab === "general" && (
            <div className="space-y-6">
              <div className="space-y-4 rounded-lg bg-gray-50 p-4">
                <InfoBlock label="Company Name" value={user.companyName} />
                <InfoBlock
                  label="Company Registration Number"
                  value={user.companyRegistrationNumber}
                />
                <InfoBlock
                  label="Tax ID (TIN/VAT)"
                  value={user.taxIdentificationNumber}
                />
                <InfoBlock label="Business Type" value={user.businessType} />
                <InfoBlock
                  label="Website URL"
                  value={
                    <a
                      href={user.websiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {user.websiteUrl}
                    </a>
                  }
                />
              </div>
            </div>
          )}
          {activeTab === "permissions" && (
            <div className="space-y-4">
              {/* This section should ideally group permissions by category */}
              <div className="space-y-1 rounded-lg bg-gray-50 p-4">
                <h4 className="text-sm font-semibold text-gray-600">
                  Assigned Permissions
                </h4>
                <ul className="list-disc space-y-1 pl-5 text-sm text-gray-700">
                  {accessSections.map((perm) => (
                    <li key={perm.id}>{perm.description}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/*
================================================================================
| 4. MAIN PAGE COMPONENT                                                     |
================================================================================
*/

export default function Client() {
  const { clientId } = useParams<{ clientId: string }>();
  const [selectedUser, setSelectedUser] = useState<ClientType | null>(null);

  const getClientById = () => {
    return backendApiClient.get(`clients/${clientId}`).json<ClientResponse>();
  };

  const {
    data: clientResponse,
    isLoading,
    error,
  } = useQuery<ClientResponse>({
    queryKey: ["client", clientId],
    queryFn: getClientById,
    enabled: !!clientId,
  });

  if (isLoading) {
    return <div className="p-6 text-center">Loading client details...</div>;
  }

  if (error || !clientResponse?.data) {
    return (
      <div className="p-6 text-center text-red-500">
        Failed to load client details.
      </div>
    );
  }

  const client = clientResponse.data;

  return (
    <div className="rounded-lg border border-gray-200 shadow-sm">
      <ClientHeader
        client={client}
        onViewMoreClick={() => setSelectedUser(client)}
      />

      <OverviewGrid client={client} />

      <DetailsModal user={selectedUser} onClose={() => setSelectedUser(null)} />
    </div>
  );
}
