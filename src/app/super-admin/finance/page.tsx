"use client";

import React, { useState } from "react";
import { FiFilter, FiDownload, FiMoreVertical } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { RxCounterClockwiseClock } from "react-icons/rx";
import Link from "next/link";


export default function Setting() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);

  const data = [
    {
      id: 1542,
      planName: "Premium",
      planType: "Monthly",
      startDate: "01-02-2024",
      endDate: "01-03-2024",
      status: { text: "Active", color: "text-green-600", dot: "bg-green-500" },
      totalRides: 500,
      ridesUsed: 250,
      ridesLeft: 250,
      lastBillingDate: "01-04-2024",
      paymentStatus: { text: "Unpaid", color: "text-orange-500" },
      method: "/paypal.png",
    },
    {
      id: 7854,
      planName: "Premium",
      planType: "Monthly",
      startDate: "01-12-2024",
      endDate: "01-01-2024",
      status: { text: "Expired", color: "text-gray-500", dot: "bg-blue-500" },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: "NA",
      paymentStatus: { text: "Paid", color: "text-green-500" },
      method: "/paypal.png",
    },
    {
      id: 1548,
      planName: "Basic",
      planType: "Monthly",
      startDate: "01-11-2023",
      endDate: "01-12-2024",
      status: { text: "Inactive", color: "text-gray-500", dot: "bg-gray-400" },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: "NA",
      paymentStatus: { text: "Paid", color: "text-green-500" },
      method: "/upi.png",
    },
    {
      id: 8956,
      planName: "Premium",
      planType: "Custom",
      startDate: "01-10-2023",
      endDate: "01-11-2023",
      status: { text: "Inactive", color: "text-gray-500", dot: "bg-pink-500" },
      totalRides: 1,
      ridesUsed: 1,
      ridesLeft: 0,
      lastBillingDate: "NA",
      paymentStatus: { text: "Paid", color: "text-green-500" },
      method: "/paypal.png",
    },
    {
      id: 4523,
      planName: "Premium",
      planType: "Monthly",
      startDate: "01-09-2023",
      endDate: "01-10-2023",
      status: { text: "Inactive", color: "text-gray-500", dot: "bg-green-500" },
      totalRides: 500,
      ridesUsed: 500,
      ridesLeft: 0,
      lastBillingDate: "NA",
      paymentStatus: { text: "Paid", color: "text-green-500" },
      method: "/paypal.png",
    },
  ];

  const TABLE_HEADERS = [
    "Subscription ID",
    "Plan Name",
    "Plan Type",
    "Start Date",
    "End Date",
    "Status",
    "Total Rides",
    "Rides Used",
    "Rides Left",
    "Last Billing Date",
    "Payment Status",
    "Method",
  ];
  

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Top Bar */}
      <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-[12px]">
  <form className="flex-1 max-w-md">
    <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
      Search
    </label>
    <div className="relative">
      <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none text-[#76787A]">
      <IoIosSearch size={20}/>

      </div>
      <input
        id="default-search"
        type="search"
        placeholder="Search here"
        className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
      />
    </div>
  </form>

  <div className="flex items-center gap-2">
    <div className="relative inline-block text-left" data-headlessui-state="">
      <button
        id="headlessui-menu-button-r2"
        type="button"
        aria-haspopup="menu"
        aria-expanded="false"
        data-headlessui-state=""
        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      >
<FiFilter size={18}/>

        Filters
      </button>
    </div>

    <button
      type="button"
      aria-label="clock"
      className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
    >
  <RxCounterClockwiseClock size={22} />
    </button>

    <button
      aria-label="download"
      type="button"
      className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
    >
<FiDownload size={18}/>

    </button>
  </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="text-[#76787A] text-[11px]">
            {TABLE_HEADERS.map((header) => (
              <th key={header} className="p-3 text-left font-medium">
                {header}
              </th>
            ))}
              <th></th>
            </tr>
          </thead>
          <tbody>
            {data.map((row, idx) => (
                <tr key={idx} className="border-b hover:bg-[#E4FFF4] text-sm">
  <td className="p-3 font-normal text-[050013] text-[11px] cursor-pointer" 
  onClick={() => setIsModalOpen(true)}>
  {row.id}
  </td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.planName}</td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.planType}</td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.startDate}</td>

{/* End Date */}
<td className="p-3 font-medium text-[050013] text-[11px] align-middle">
  <div className="flex items-center gap-2">
    <span className={`w-2 h-2 rounded-full ${row.status.dot}`}></span>
    {row.endDate}
  </div>
</td>

{/* Status */}
<td className="p-3 font-normal text-[11px] font-medium align-middle">
  <div className="flex items-center gap-2">
    <span
      className={`w-2 h-2 rounded-full ${
        row.status.text === "Active"
          ? "bg-[#13BB76]"
          : row.status.text === "Inactive"
          ? "bg-[#76787A]"
          : "bg-orange-500"
      }`}
    ></span>
    <span
      className={
        row.status.text === "Active"
          ? "text-[#13BB76]"
          : row.status.text === "Inactive"
          ? "text-[#76787A]"
          : "text-orange-500"
      }
    >
      {row.status.text}
    </span>
  </div>
</td>


  <td className="p-3 font-normal text-[050013] text-[11px]">{row.totalRides}</td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.ridesUsed}</td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.ridesLeft}</td>
  <td className="p-3 font-normal text-[050013] text-[11px]">{row.lastBillingDate}</td>

  {/* Payment Status with dot + text */}
  <td className="p-3 text-[11px] font-medium flex items-center gap-2">
    <span
      className={`w-2 h-2 rounded-full ${
        row.paymentStatus.text === "Paid" ? "bg-green-500" : "bg-orange-500"
      }`}
    ></span>
    <span
      className={
        row.paymentStatus.text === "Paid" ? "text-green-500" : "text-orange-500"
      }
    >
      {row.paymentStatus.text}
    </span>
  </td>

  <td className="font-normal text-[050013] text-[11px]">
    <img src={"/images/paypal.svg"} alt="Method" className="w-10 h-10" />
  </td>
  <td className="p-3 font-normal text-[050013] text-[18px]">
    <FiMoreVertical className="text-[#76787A] cursor-pointer" />
  </td>
</tr>



            ))}
          </tbody>
        </table>
      </div>



      {isModalOpen && (
       <div className="fixed inset-0 z-999 flex custom-scrollbar">
       <div className="absolute inset-0 bg-black/30" onClick={() => setSelectedUser(null)}></div>
       <div className="ml-auto w-full max-w-md bg-white h-full shadow-xl transform translate-x-0 transition-transform duration-300 ease-out rounded-tl-[30px] rounded-bl-[30px] overflow-y-auto">
         <div className="flex justify-between items-center mb-4 px-6 py-5 bg-tables">
           <h2 className="text-[20px] font-normal text-[#050013]">
             View User Details
           </h2>
           <button
  type="button"
  onClick={() => {
    console.log("Close button clicked");
    setSelectedUser(null);
  }}
  className="text-gray-500 hover:text-red-500"
>
  ✕
</button>
         </div>
   
         <div className="px-6 py-6 text-[13px] space-y-4 bg-tables">
           <div>
             <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-1">Subscription Details</h3>
             <p className="grid grid-cols-2 gap-2 py-4 px-4">
             <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Subscription ID</span> 1542</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Plan Name</span> Premium</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Plan Type</span> Monthly</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Start Date</span> 01-02-2024</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">End Date</span> 01-03-2024</div>
               <div className="grid text-[12px] text-[#050013] font-medium mb-4">
                <span className="mb-1 text-[#76787A] font-normal text-[13px]">Status:</span>
                <span className="inline-flex items-center gap-1 text-[#13BB76]">
                 <span className="w-2 h-2 rounded-full bg-[#13BB76]"></span>
                 Active
                 </span>
                </div>
             </p>
           </div>

           <div>
             <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-1">Ride Details</h3>
             <p className="grid grid-cols-2 gap-2 py-4 px-4">
             <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Total Rides</span> 500</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Rides Used</span> 250</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Rides Left</span> 250</div>
             </p>
           </div>


           <div>
             <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-1">Payment Details</h3>
             <p className="grid grid-cols-2 gap-2 py-4 px-4">
             <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Last Billing Date</span> 01-04-2024</div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Oustanding Balance</span> 
               <span className="inline-flex items-center gap-1 text-[#050013]">
                 <span className="w-2 h-2 rounded-full bg-[#13BB76]"></span>
                 € 388.00
                 </span>
               </div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Payment Status</span>
               <span className="inline-flex items-center gap-1 text-[#FFC107] text-[12px]">
                 <span className="w-2 h-2 rounded-full bg-[#13BB76]"></span>
                 Unpaid
                 </span>
               </div>
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
               <span className="mb-1 text-[#76787A] font-normal text-[13px]">Payment Method</span> 
               <img src="/images/paypal.svg" alt="Icon" className="object-contain" />
               </div>
             </p>
           </div>



         </div>
       </div>
     </div>
      )}
    </div>


  );
}
