import React from "react";
import { FiFilter, FiDownload, FiMoreVertical } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { RxCounterClockwiseClock } from "react-icons/rx";
import Link from "next/link";


const members = [
  {
    clientname: "TechNova",
    date: "11-01-2025",
    passenger: "10",
    status: "Active",
    plan: "Basic",
  },
  {
    clientname: "InnovaTech",
    date: "11-01-2025",
    passenger: "10",
    status: "Active",
    plan: "Basic",
  },
];
const circleColors = [
    { bg: "#FFEED4", text: "#CC7A00" }, // darker orange
    { bg: "#D6FFEE", text: "#008B5B" }, // darker green
    { bg: "#FBE9FF", text: "#800080" }, // darker purple
  ];
export default function MembersTable() {
  return (
    <div className="p-4">
      <div className="flex justify-end items-center mb-4">
        <button className="bg-[#3707EF] text-white px-4 py-2 rounded-full text-sm hover:bg-[#4338ca] transition">
          + Add New Member
        </button>
      </div>

      <div className="overflow-x-auto bg-white rounded-lg border">
         <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-lg">
          <form className="flex-1 max-w-md">
            <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none text-[#76787A]">
              <IoIosSearch size={20}/>
        
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
              />
            </div>
          </form>
        
          <div className="flex items-center gap-2">
            <div className="relative inline-block text-left" data-headlessui-state="">
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              >
        <FiFilter size={18}/>
        
                Filters
              </button>
            </div>
        
            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
             <RxCounterClockwiseClock size={22} />
            </button>
        
            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
        <FiDownload size={18}/>
        
            </button>
          </div>
              </div>
        <table className="min-w-full divide-y divide-gray-200 text-[12px]">
          <thead>
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              {["Client Name", "Date Added", "No of Passengers", "Status", "Subscription Plan", ""].map(
                (header) => (
                  <th
                    key={header}
                    className="whitespace-nowrap px-4 py-3 text-left font-medium text-[#76787A]"
                  >
                    {header}
                  </th>
                )
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {members.map((member, idx) => (
              <tr
                key={idx}
                className="hover:bg-[#E4FFF4] cursor-pointer transition"
              >
                <td className="px-4 py-3 text-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </td>
                <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] flex items-center gap-2">
                   {/* Circle with initials */}
                  <div
                    className="w-7 h-7 rounded-full flex items-center justify-center text-[10px] font-semibold"
                    style={{
                      backgroundColor: circleColors[idx % circleColors.length].bg,
                      color: circleColors[idx % circleColors.length].text,
                    }}
                  >
                    {member.clientname
                      .replace(/([a-z])([A-Z])/g, "$1 $2") // insert space before capital letters
                      .split(" ")
                      .map((word) => word[0])
                      .join("")
                      .toUpperCase()}
                  </div>

                  {/* Client Name */}
                  {member.clientname}
                </td>

                <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                  {member.date}
                </td>
                <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                  {member.passenger}
                </td>
                <td
  className={`whitespace-nowrap px-4 py-3 font-normal text-[12px] flex items-center gap-1 ${
    member.status === "Active"
      ? "text-[#13BB76]" // green
      : member.status === "Expired"
      ? "text-[#FF0000]" // red (you can change to any color you want)
      : ""
  }`}
>
  <span
    className={`h-2 w-2 rounded-full inline-block ${
      member.status === "Active"
        ? "bg-[#13BB76]"
        : member.status === "Expired"
        ? "bg-[#FF0000]"
        : ""
    }`}
  ></span>
  {member.status}
</td>
                <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                  {member.plan}
                </td>
                <td className="text-[16px] px-4 py-3 text-[#76787A] select-none cursor-pointer">
                  <FiMoreVertical />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
