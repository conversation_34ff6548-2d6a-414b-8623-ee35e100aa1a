"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { FiEdit } from "react-icons/fi";
import { GoDownload } from "react-icons/go";
import { FiFilter, FiDownload, FiMoreVertical } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { TbUsers } from "react-icons/tb";
import { BsFilter } from "react-icons/bs";



export default function Setting() {
  const [selectedUser, setSelectedUser] = useState(null);
  const [activeTab, setActiveTab] = useState("general");
  
  const members = [
    {
      id: "TechNova",
      name: "11-01-2025",
      contact: "10",
      status: "Active",
      city: "Basic",
      rides:"abc",
    },
    {
      id: "TechNova",
      name: "11-01-2025",
      contact: "10",
      status: "Suspended",
      city: "Basic",
      rides:"abc",
    },
  ];

    const accessSections = [
      {
        title: "User & Role Management",
        items: ["Create, edit, delete Admins, Dispatchers, Drivers, Users", "Assign Admins to taxi companies","Manage permissions for all roles"]
      },
      {
        title: "Booking & Dispatch Control",
        items: ["View, edit, and manage all bookings", "Assign/reassign drivers to rides", "View driver live locations"]
      },
      {
        title: "Company & Financial Management",
        items: ["Add, edit, or remove taxi companies", "Manage company subscription plans", "View & modify company-specific pricing and fare structures"]
      },
      {
        title: "System & Policy Settings",
        items: ["Define platform-wide fare policies", "Set geofencing rules & restrictions", "Control global discount and promo policies"]
      },
      {
        title: "Reporting & Analytics",
        items: ["View and export reports on revenue, ride activity, and system performance", "Monitor driver performance & customer ratings", "Analyze dispatcher efficiency"]
      }
    ];
  

  const userDetails = {
    name: "Silverline Solutions",
    regestration: "*********",
    tax: "*********",
    businesstype:"LLC",
    url:"www.silverline.com",
  };

  return (
    <div className="rounded-lg border border-gray-200">
      {/* Top Row */}
      <div className="rounded-t-lg bg-white p-6 flex flex-col lg:flex-row lg:items-start justify-between gap-6 border-b">
        
        {/* Left: Logo + Company Info */}
        <div className="flex items-center gap-4 min-w-[250px]">
          <div className="w-18 h-18 rounded-full bg-[#E4FFF4] text-[24px] text-[#4D695D] font-normal flex items-center justify-center">
            TS
          </div>
          <div>
            <h2 className="text-[24px] text-[#050013] font-normal">TechNova  Solutions</h2>
            <div className="flex items-center gap-2 text-[11px] text-[#13BB76] font-medium">
              <span className="w-2 h-2 bg-[#13BB76] rounded-full"></span>
              Active
            </div>
          </div>
        </div>

        {/* Middle: Contact Details */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-center sm:gap-12 text-sm flex-1 border-l">
          <div className="space-y-3">
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Email Id : </span> <EMAIL>
            </p>
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Phone no : </span> *************
            </p>
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Subscription Plan : </span> Basic
            </p>
          </div>
          <div className="space-y-3">
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Primary Contact Name : </span> Oliver Thompson
            </p>
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Tax Identification Number (TIN/VAT): </span> 45485
            </p>
            <p className="font-medium text-[#050013] text-[14px]">
              <span className="font-normal text-[#76787A] text-[13px]">Address : </span> 12 Street, The Hague, Netherlands
            </p>
            <p className="text-right">
              <button
                onClick={() => setSelectedUser(userDetails)}
                className="text-[#3324E3] text-[13px] underline"
              >
                View More
              </button>
            </p>
          </div>
        </div>

        {/* Right: Action Buttons */}
        <div className="flex items-center gap-1 justify-end">
          <button className="p-2 rounded-md hover:bg-gray-100 text-[#76787A]" title="Edit"><FiEdit size={18} /></button>
          <button className="p-2 rounded-md hover:bg-gray-100 text-[#76787A]" title="Download"><GoDownload size={20} /></button>
        </div>
      </div>

      {/* Overview Section */}
      <div className="rounded-b-lg pt-6 bg-[#F6F8FB]">
      <h3 className="text-[14px] text-[#3324E3] font-normal ml-[20px] flex items-center gap-2 border-b border-b-[3px] border-[#3324E3] w-fit pb-1">
  <TbUsers size={20} />
  Passengers
</h3>
              <div className="overflow-x-auto border-t divide-y">
                 <div className="header-bar flex items-center justify-between py-2 px-3 bg-table-head rounded-t-lg">
                  <form className="flex-1 max-w-md">
                    <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
                      Search
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none text-[#76787A]">
                      <IoIosSearch size={20}/>
                
                      </div>
                      <input
                        id="default-search"
                        type="search"
                        placeholder="Search here"
                        className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
                      />
                    </div>
                  </form>
                
                  <div className="flex items-center gap-2">
                    
                  <div className="relative inline-block text-left" data-headlessui-state="">
                      <button
                        id="headlessui-menu-button-r2"
                        type="button"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        data-headlessui-state=""
                        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      >
                <BsFilter size={18}/>
                
                        Status
                      </button>
                    </div>

                    <div className="relative inline-block text-left" data-headlessui-state="">
                      <button
                        id="headlessui-menu-button-r2"
                        type="button"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        data-headlessui-state=""
                        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      >
                <BsFilter size={18}/>
                
                        Company
                      </button>
                    </div>

                    <div className="relative inline-block text-left" data-headlessui-state="">
                      <button
                        id="headlessui-menu-button-r2"
                        type="button"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        data-headlessui-state=""
                        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      >
                <BsFilter size={18}/>
                
                        City
                      </button>
                    </div>



                    <div className="relative inline-block text-left" data-headlessui-state="">
                      <button
                        id="headlessui-menu-button-r2"
                        type="button"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        data-headlessui-state=""
                        className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      >
                <FiFilter size={18}/>
                
                        Filters
                      </button>
                    </div>
                
                    <button
                      type="button"
                      aria-label="clock"
                      className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    >
                     <RxCounterClockwiseClock size={22} />
                    </button>
                
                    <button
                      aria-label="download"
                      type="button"
                      className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    >
                <FiDownload size={18}/>
                
                    </button>
                  </div>
                      </div>
                <table className="min-w-full divide-y divide-gray-200 text-[12px]">
                  <thead>
                    <tr>
                      <th className="px-4 py-3">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                      </th>
                      {["ID", "Name", "Contact No", "Status", "City", "Rides", ""].map(
                        (header) => (
                          <th
                            key={header}
                            className="whitespace-nowrap px-4 py-3 text-left font-medium text-[#76787A]"
                          >
                            {header}
                          </th>
                        )
                      )}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {members.map((member, idx) => (
                      <tr
                        key={idx}
                        className="hover:bg-[#E4FFF4] cursor-pointer transition"
                      >
                        <td className="px-4 py-3 text-center">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          />
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] flex items-center gap-2">

        
          {/* Client Name */}
          {member.id}
        </td>
        
                        <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                          {member.name}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                          {member.contact}
                        </td>
                        <td
          className={`whitespace-nowrap px-4 py-3 font-normal text-[12px] flex items-center gap-1 ${
            member.status === "Active"
              ? "text-[#13BB76]" // green
              : member.status === "Suspended"
              ? "text-[#DC3545]" // red (you can change to any color you want)
              : ""
          }`}
        >
          <span
            className={`h-2 w-2 rounded-full inline-block ${
              member.status === "Active"
                ? "bg-[#13BB76]"
                : member.status === "Suspended"
                ? "bg-[#13BB76]"
                : ""
            }`}
          ></span>
          {member.status}
        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                          {member.city}
                        </td>
                        <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
                          {member.rides}
                        </td>
                        <td className="text-[16px] px-4 py-3 text-[#76787A] select-none cursor-pointer">
                          <FiMoreVertical />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
      </div>
        {/* View detail modal */}
        {selectedUser && (
       <div className="fixed inset-0 z-[999] flex custom-scrollbar">
       <div
         className="absolute inset-0 bg-black/30"
         onClick={() => setSelectedUser(null)}
       ></div>
 
       <div className="ml-auto w-full max-w-md bg-white h-full shadow-xl transform translate-x-0 transition-transform duration-300 ease-out rounded-tl-[30px] rounded-bl-[30px] overflow-y-auto">
         {/* Header */}
         <div className="flex justify-between items-center mb-4 px-6 py-5 bg-tables">
           <h2 className="text-[20px] font-normal text-[#050013]">
           View Details
           </h2>
           <button
             onClick={() => setSelectedUser(null)}
             className="text-gray-500 hover:text-red-500"
           >
             ✕
           </button>
         </div>
 
         {/* Tabs */}
         <div className="flex border-b border-gray-200 gap-6 justify-start px-6">
  {["general", "permissions"].map((tab) => (
    <button
      key={tab}
      className={`py-2 px-2 text-center text-[13px] font-normal relative ${
        activeTab === tab ? "text-[#3324E3] font-semibold" : "text-gray-500"
      }`}
      onClick={() => setActiveTab(tab)}
    >
      <span className="relative inline-block">
        {tab === "general" ? "General Info" : "Permissions"}
        {activeTab === tab && (
          <span className="absolute -bottom-[8px] left-0 right-0 mx-auto w-full border-b-2 border-[#3324E3]"></span>
        )}
      </span>
    </button>
  ))}
</div>


 
         <div className="px-6 py-6 text-[13px] space-y-4 bg-tables">
           {/* General Info Tab */}
           {activeTab === "general" && (
             <>
               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Company Information
                 </h3>
                 <div className="grid grid-cols-2 gap-2 py-4 px-4">
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Company Name
                     </span>{" "}
                     {selectedUser.name}
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Legal Name
                     </span>{" "}
                     ABC Transport Solutions Pvt. Ltd.
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Company Registration Number
                     </span>{" "}
                     {selectedUser.regestration}
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Tax Identification Number (TIN/VAT)
                     </span>{" "}
                     {selectedUser.tax}
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Business Type:
                     </span>{" "}
                     {selectedUser.businesstype}
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Website URL
                     </span>{" "}
                     {selectedUser.url}
                   </div>
                 </div>
               </div>
 
               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Contact Information
                 </h3>
                 <p className="grid grid-cols-2 gap-2 py-4 px-4">
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Primary Contact Name
                     </span>{" "}
                     Toyota Prius
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Designation
                     </span>{" "}
                     Owner
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Email Address
                     </span>{" "}
                     <EMAIL>
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Phone Number
                     </span>{" "}
                     ****** 567 890
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Alternative Contact
                     </span>{" "}
                     ******* 567 890
                   </div>
                 </p>
               </div>
               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Business Address
                 </h3>
                 <p className="grid grid-cols-2 gap-2 py-4 px-4">
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Head Office
                     </span>{" "}
                     123 Main Street, New York, NY
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     City
                     </span>{" "}
                     New York
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     State/Province:
                     </span>{" "}
                     New York
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Country
                     </span>{" "}
                     USA
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Postal Code
                     </span>{" "}
                     100001
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Branch Locations
                     </span>{" "}
                     Los Angeles, CA
                     Chicago,IL
                   </div>
                 </p>
               </div>

               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Payment & Billing
                 </h3>
                 <p className="grid grid-cols-2 gap-2 py-4 px-4">
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Billing Cycle
                     </span>{" "}
                     Monthly
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Invoice Email
                     </span>{" "}
                     <EMAIL>
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                        <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                         Contract
                        </span>
                        <div className="flex items-center gap-2">
                         <img src="/images/contract.png" alt="Contract Icon" className="w-4 h-4"/>
                         <span className="underline">View Contract</span>
                    </div>
                    </div>

                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Legal Representative:
                     </span>{" "}
                     John Doe
                   </div>
                 </p>
               </div>


               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Service Details
                 </h3>
                 <p className="grid grid-cols-2 gap-2 py-4 px-4">
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Type of Service 
                     </span>{" "}
                     Standard Taxi
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Operating Regions
                     </span>{" "}
                     Newyork, Los Angeles, Chicago                   
                     </div>
                     <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Fleet Size
                     </span>{" "}
                     150 Vehicles         
                     </div>
                     <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Available Vehicle Types
                     </span>{" "}
                     Sedan, SUV                
                     </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                        <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                        Special Services  
                        </span>
                        <div className="flex items-center gap-2">
                         <img src="/images/chair.svg" alt="Contract Icon" className="w-6 h-6"/>
                         <img src="/images/seat-belt.svg" alt="Contract Icon" className="w-6 h-6"/>
                    </div>
                    </div>
                 </p>
               </div>


               <div>
                 <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                 Licensing & Compliance
                 </h3>
                 <p className="grid grid-cols-2 gap-2 py-4 px-4">
                 <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                        <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                        Business License
                        </span>
                        <div className="flex items-center gap-2">
                         <img src="/images/contract.png" alt="Contract Icon" className="w-4 h-4"/>
                         <span className="underline">View Contract</span>
                    </div>
                    </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Insurance Company
                     </span>{" "}
                     HeapHealth
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Policy Number
                     </span>{" "}
                     78451221
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Expiry
                     </span>{" "}
                     12-12-2025
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Regulatory Certificated
                     </span>{" "}
                     <span className="underline">Transport Authority Approval</span>
                   </div>
                   <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Permit Expiry
                     </span>{" "}
                     10-03-2026
                   </div>
                 </p>
               </div>



             </>
           )}
 
           {/* Permissions Tab */}
           {activeTab === "permissions" && (
             <div className="mb-12">
               <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
               Access Control and permissions
               </h3>
               {accessSections.map((section, index) => (
                 <div key={index}>
                   <h4 className="text-[13px] font-semibold text-[#76787A] px-4 py-2 mt-6">
                     {section.title}
                   </h4>
                   <ul className="list-disc ml-5 text-[#050013] text-[14px] px-6">
                     {section.items.map((item, idx) => (
                       <li className="my-2" key={idx}>
                         {item}
                       </li>
                     ))}
                   </ul>
                 </div>
               ))}


                <div className="mt-6">
                <h3 className="text-[13px] font-medium bg-white px-4 py-2 rounded-full text-[#76787A] mb-2">
                Edit Logs
               </h3>
               <p className="grid grid-cols-2 gap-2 py-4 px-4">
               <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Last Modified
                     </span>{" "}
                     20-01-2025
                </div>
                <div className="grid text-[14px] text-[#050013] font-medium mb-4">
                     <span className="mb-1 text-[#76787A] font-normal text-[13px]">
                     Last Modified By
                     </span>{" "}
                     Jack Manosh
                </div>
                </p>
                </div>

             </div>
           )}
         </div>
       </div>
     </div>
      )}
    </div>
  );
}

