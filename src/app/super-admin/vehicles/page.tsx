import React from "react";
import { FiFilter, FiDownload, FiMoreVertical } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { MdOutlineStar } from "react-icons/md";
import Link from "next/link";

import { FaUserTie, FaTruck, FaDollarSign, FaCheckCircle } from "react-icons/fa";

export default function StatsCards() {
  const stats = [
    {
      title: "Total Active Vehicles",
      value: "14",
      icon: <img src="/images/active-vehicle.svg" alt="Active Vehicle"/>,
      color: "text-green-500",
    },
    {
      title: "Verified Vehicles",
      value: "11",
      icon: <img src="/images/verified-vehicle.svg" alt="Verified Vehicle"/>,
      color: "text-red-500",
    },
    {
      title: "Pending Verification",
      value: "04",
      icon: <img src="/images/pending-vehicle.svg" alt="Pending Vehicle"/>,
      color: "text-green-500",
    },
    {
      title: "Expired Insurance",
      value: "03",
      icon: <img src="/images/expired-vehicle.svg" alt="Expired Vehicle"/>,
      color: "text-yellow-500",
    },
  ];

  const VERIFICATION_STYLES: Record<string, { text: string; dot: string }> = {
    Verified: { text: "text-[#13BB76]", dot: "bg-[#13BB76]" },
    Rejected: { text: "text-[#76787A]", dot: "bg-[#76787A]" },
    Pending: { text: "text-[#FF8C00]", dot: "bg-[#FF8C00]" },
  };
  
  const INSURANCE_STYLES: Record<string, { text: string; dot: string }> = {
    Active: { text: "text-[#13BB76]", dot: "bg-[#13BB76]" }, // green
    Expired: { text: "text-[#050013]", dot: "bg-[#050013]" }, // black
  };

  
const members = [
  {
    type: "Sedan",
    model: "Hyundai Tucson",
    fueltype: "Petrol",
    licenseplate:"NL-1234-AB",
    verification:"Pending",
    owner:"Silverline Solutions",
    insurancestatus: "Active",
    policyno:"INSP-0001256",
    date:"20-05-2025",
  },
  {
    type: "SUV",
    model: "Toyota Corolla",
    fueltype: "Electric",
    licenseplate:"NL-5678-CD",
    verification:"Verified",
    owner:"Silverline Solutions",
    insurancestatus: "Expired",
    policyno:"INSP-0001257",
    date:"15-12-2024",
  },
  {
    type: "XUV",
    model: "Toyota Corolla",
    fueltype: "Electric",
    licenseplate:"NL-5678-CD",
    verification:"Pending",
    owner:"Silverline Solutions",
    insurancestatus: "Expired",
    policyno:"INSP-0001257",
    date:"15-12-2024",
  },
  {
    type: "Van",
    model: "Toyota Corolla",
    fueltype: "Electric",
    licenseplate:"NL-5678-CD",
    verification:"Rejected",
    owner:"Silverline Solutions",
    insurancestatus: "Active",
    policyno:"INSP-0001257",
    date:"15-12-2024",
  },
];


  return (
    <div className="p-4">
      <div className="flex justify-end items-center mb-4">
        <button className="bg-[#3707EF] text-white px-4 py-2 rounded-full text-sm hover:bg-[#4338ca] transition">
          + Add New Member
        </button>
      </div>


      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 my-6">
      {stats.map((item, index) => (
        <div
          key={index}
          className="flex justify-between items-center p-4 bg-white rounded-xl border"
        >
          <div>
            <p className="text-[#76787A] text-[13px] font-normal">{item.title}</p>
            <h2 className="text-[26px] font-medium text-[#050013]">{item.value}</h2>
          </div>
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${item.color}`}>
            {item.icon}
          </div>
        </div>
      ))}
    </div>





      <div className="overflow-x-auto bg-white rounded-lg border">
         <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-lg">
          <form className="flex-1 max-w-md">
            <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none text-[#76787A]">
              <IoIosSearch size={20}/>
        
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
              />
            </div>
          </form>
        
          <div className="flex items-center gap-2">
            <div className="relative inline-block text-left" data-headlessui-state="">
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              >
        <FiFilter size={18}/>
        
                Filters
              </button>
            </div>
        
            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
               <RxCounterClockwiseClock size={22} />
            </button>
        
            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
        <FiDownload size={18}/>
        
            </button>
          </div>
              </div>
        <table className="min-w-full divide-y divide-gray-200 text-[12px]">
          <thead>
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              {["Type", "Model", "Fuel Type", "License Plate", "Verification", "Owner","Insurance Status","Policy No","Date", ""].map(
                (header) => (
                  <th
                    key={header}
                    className="whitespace-nowrap px-4 py-3 text-left font-medium text-[#76787A]"
                  >
                    {header}
                  </th>
                )
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {members.map((member, idx) => (
                <tr
  key={idx}
  className="hover:bg-[#E4FFF4] cursor-pointer transition"
>
  <td className="px-4 py-3 text-center">
    <input
      type="checkbox"
      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
    />
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.type}
  </td>
  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.model}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.fueltype}
  </td>
  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.licenseplate}
  </td>
   {/* VERIFICATION column */}
   <td
  className={`whitespace-nowrap px-4 py-3 font-normal text-[11px] flex items-center gap-1 ${
    VERIFICATION_STYLES[member.verification]?.text || ""
  }`}
>
  <span
    className={`h-2 w-2 rounded-full inline-block ${
      VERIFICATION_STYLES[member.verification]?.dot || ""
    }`}
  ></span>
  {member.verification}
</td>
  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.owner}
  </td>

  <td
  className={`whitespace-nowrap px-4 py-3 font-normal text-[12px] flex items-center gap-1 ${
    INSURANCE_STYLES[member.insurancestatus]?.text || ""
  }`}
>
  <span
    className={`h-2 w-2 rounded-full inline-block ${
      INSURANCE_STYLES[member.insurancestatus]?.dot || ""
    }`}
  ></span>
  {member.insurancestatus}
</td>



  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1">
    {member.policyno}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.date}
  </td>

  <td className="text-[16px] px-4 py-3 text-[#76787A] select-none cursor-pointer">
    <FiMoreVertical />
  </td>
</tr>

            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
