import React from "react";
import { FiFilter, FiDownload, FiMoreVertical } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { MdOutlineStar } from "react-icons/md";
import { RxCounterClockwiseClock } from "react-icons/rx";

import Link from "next/link";

import { FaUserTie, <PERSON>a<PERSON><PERSON>ck, FaDollarSign, FaCheckCircle } from "react-icons/fa";

// statusColors.ts
export const STATUS_COLORS: Record<
  string,
  { text: string; dot: string }
> = {
  Active: { text: "text-[#13BB76]", dot: "bg-[#13BB76]" },
  Inactive: { text: "text-[#76787A]", dot: "bg-[#76787A]" },
  Suspended: { text: "text-[#050013]", dot: "bg-[#050013]" },
  Enroute: { text: "text-[#1E90FF]", dot: "bg-[#1E90FF]" },
  "On trip": { text: "text-[#FF8C00]", dot: "bg-[#FF8C00]" },
};

// verificationColors.ts
export const VERIFICATION_COLORS: Record<
  string,
  { text: string; dot: string }
> = {
  Verified: { text: "text-[#13BB76]", dot: "bg-[#13BB76]" },
  Rejected: { text: "text-[#76787A]", dot: "bg-[#76787A]" },
  Pending: { text: "text-[#FF8C00]", dot: "bg-[#FF8C00]" },
};


export default function StatsCards() {
  const stats = [
    {
      title: "Total Active Drivers",
      value: "06",
      icon: <img src="/images/active-driver.svg" alt="Active Drivers"/>,
      color: "bg-green-100 text-green-500",
    },
    {
      title: "Unassigned Drivers",
      value: "05",
      icon: <img src="/images/unassigned-driver.svg" alt="Unassigned Drivers"/>,
      color: "bg-red-100 text-red-500",
    },
    {
      title: "Total Earnings",
      value: "€16,0000",
      icon: <img src="/images/total-earning.svg" alt="Unassigned Drivers"/>,
      color: "bg-green-100 text-green-500",
    },
    {
      title: "Pending Verification",
      value: "03",
      icon: <img src="/images/pending-verification.svg" alt="Unassigned Drivers"/>,
      color: "bg-yellow-100 text-yellow-500",
    },
  ];
// statusConfig.js
const STATUS_CONFIG = {
  Active: { textClass: "text-[#13BB76]", dotClass: "bg-[#13BB76]" },
  Inactive: { textClass: "text-[#76787A]", dotClass: "bg-[#76787A]" },
  Suspended: { textClass: "text-[#050013]", dotClass: "bg-[#050013]" },
  Enroute: { textClass: "text-[#1E90FF]", dotClass: "bg-[#1E90FF]" },
  "On trip": { textClass: "text-[#FF8C00]", dotClass: "bg-[#FF8C00]" },
};

const members = [
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"Active",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Verified",
  },
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"Active",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Verified",
  },
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"Enroute",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Pending",
  },
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"Suspended",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Rejected",
  },
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"Inactive",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Pending",
  },
  {
    id: "124432",
    name: "Emily Davis",
    contact: "+1234567891",
    status:"On trip",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Pending",
  },
  {
    id: "124433",
    name: "Morris Seal",
    contact: "+1234567891",
    status:"Inactive",
    shift:"Day Shift",
    rating:"4.5",
    region: "Eindhoven",
    vdetail:"EV  MT-9826-SA",
    rides:"183",
    earning:"€16,000",
    verification:"Rejected",
  },
];


  return (
    <div className="p-4">
      <div className="flex justify-end items-center mb-4">
        <button className="bg-[#3707EF] text-white px-4 py-2 rounded-full text-sm hover:bg-[#4338ca] transition">
          + Add New Drivers 
        </button>
      </div>


      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 my-6">
      {stats.map((item, index) => (
        <div
          key={index}
          className="flex justify-between items-center p-4 bg-white rounded-xl border"
        >
          <div>
            <p className="text-[#76787A] text-[13px] font-normal">{item.title}</p>
            <h2 className="text-[26px] font-medium text-[#050013]">{item.value}</h2>
          </div>
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${item.color}`}>
            {item.icon}
          </div>
        </div>
      ))}
    </div>





      <div className="overflow-x-auto bg-white rounded-lg border">
         <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-lg">
          <form className="flex-1 max-w-md">
            <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
              Search
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none text-[#76787A]">
              <IoIosSearch size={20}/>
        
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white border-0 rounded-full shadow-[0_10px_40px_0_#0000000D]"
              />
            </div>
          </form>
        
          <div className="flex items-center gap-2">
            <div className="relative inline-block text-left" data-headlessui-state="">
              <button
                id="headlessui-menu-button-r2"
                type="button"
                aria-haspopup="menu"
                aria-expanded="false"
                data-headlessui-state=""
                className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              >
        <FiFilter size={18}/>
        
                Filters
              </button>
            </div>
        
            <button
              type="button"
              aria-label="clock"
              className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
      <RxCounterClockwiseClock size={22}/>

            </button>
        
            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            >
        <FiDownload size={16}/>
        
            </button>
          </div>
              </div>
        <table className="min-w-full divide-y divide-gray-200 text-[12px]">
          <thead>
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              {["ID", "Name", "Contact No", "Status", "Shift", "Rating","Region","Vehicle Details","Rides","Earnings","Verification", ""].map(
                (header) => (
                  <th
                    key={header}
                    className="whitespace-nowrap px-4 py-3 text-left font-medium text-[#76787A]"
                  >
                    {header}
                  </th>
                )
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {members.map((member, idx) => (
                <tr
  key={idx}
  className="hover:bg-[#E4FFF4] cursor-pointer transition"
>
  <td className="px-4 py-3 text-center">
    <input
      type="checkbox"
      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
    />
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.id}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.name}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.contact}
  </td>

  {/* STATUS column */}
  <td
  className={`whitespace-nowrap px-4 py-3 font-normal text-[11px] flex items-center gap-1 ${
    STATUS_COLORS[member.status]?.text || ""
  }`}
>
  <span
    className={`h-2 w-2 rounded-full inline-block ${
      STATUS_COLORS[member.status]?.dot || ""
    }`}
  ></span>
  {member.status}
</td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px]">
    {member.shift}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1 flex items-center">
    {member.rating}
    <MdOutlineStar size={14} className="text-[#F79E18]" />
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1">
    {member.region}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1">
    {member.vdetail}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1">
    {member.rides}
  </td>

  <td className="whitespace-nowrap px-4 py-3 text-[#050013] font-normal text-[12px] gap-1">
    {member.earning}
  </td>

  {/* VERIFICATION column */}
  <td
  className={`whitespace-nowrap px-4 py-3 font-normal text-[11px] flex items-center gap-1 ${
    VERIFICATION_COLORS[member.verification]?.text || ""
  }`}
>
  <span
    className={`h-2 w-2 rounded-full inline-block ${
      VERIFICATION_COLORS[member.verification]?.dot || ""
    }`}
  ></span>
  {member.verification}
</td>
 

  <td className="text-[16px] px-4 py-3 text-[#76787A] select-none cursor-pointer">
    <FiMoreVertical />
  </td>
</tr>

            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
