"use client";
import { HeaderTitleProvider } from "@/context/HeaderTitleContext";
import { useSidebar } from "@/context/SidebarContext";
import AppHeader from "@/layout/AppHeader";
import AppSidebar from "@/layout/AppSidebar";
import Backdrop from "@/layout/Backdrop";
import { usePathname } from "next/navigation";
import React, { ReactNode } from "react";

interface SuperAdminLayoutProps {
  children: ReactNode;
}

export default function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  const pathname = usePathname();
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();

  const mainContentMargin = isMobileOpen
      ? "ml-0"
      : isExpanded || isHovered
        ? "lg:ml-[235px]"
        : "lg:ml-[90px]",
    mainContent =
      "mx-auto max-w-screen-2xl bg-white mb-[15%] sm:mb-[5%]" +
      (pathname != "/driver/live" && pathname != "/driver/start-ride"
        ? " p-4"
        : "");
  return (
    <HeaderTitleProvider>
      <div
        className={
          pathname != "/super-admin/login" ? "min-h-screen xl:flex" : ""
        }
      >
        {/* <AppHeader /> */}
        {pathname != "/super-admin/login" && (
          <>
            <AppSidebar />
            <Backdrop />
          </>
        )}

        <div
          className={
            pathname != "/super-admin/login"
              ? `flex-1 bg-white transition-all duration-300 ease-in-out ${mainContentMargin}`
              : ""
          }
        >
          {pathname != "/super-admin/login" && <AppHeader />}
          <main className={pathname != "/super-admin/login" ? mainContent : ""}>
            {children}
          </main>
        </div>
      </div>
    </HeaderTitleProvider>
  );
}
