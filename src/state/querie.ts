import { backendApiClient } from "@/utils/apiClient";
export async function signInQuery(data: {
  username: string;
  password: string;
}): Promise<any> {
  const response = await backendApiClient
    .post("auth/login/vehicle-mgt-sys", {
      json: data,
    })
    .json();

  return response;
}

export async function superAdminSignInQuery(data: {
  email: string;
  password: string;
}): Promise<any> {
  const response = await backendApiClient
    .post("superadmin/login", {
      json: data,
    })
    .json();

  return response;
}
