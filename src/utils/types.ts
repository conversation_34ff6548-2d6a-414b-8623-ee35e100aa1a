export interface SuperAdminType {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  phoneNumber: string;
  lastLoginAt: string;
  createdAt: string;
  updatedAt: string;
  permissions: PermissionType[];
}

export interface PermissionType {
  id: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClientType {
  id: string;
  superAdminId: string;
  companyName: string;
  legalName: string;
  companyRegistrationNumber: string;
  taxIdentificationNumber: string;
  businessType: string;
  websiteUrl: string;
  shortDescription: string;
  logoUrl?: string;
  primaryContactName: string;
  primaryContactDesignation: string;
  primaryContactEmail: string;
  primaryContactPhone: string;
  alternativeContactName: string;
  alternativeContactEmail: string;
  alternativeContactPhone: string;
  headOfficeAddress: string;
  city: string;
  stateProvince: string;
  country: string;
  postalCode: string;
  branchLocation: string;
  billingCycle: string;
  invoiceEmail: string;
  contractUrl?: string;
  legalRepresentativeName: string;
  legalRepresentativeSignatureUrl?: string;
  serviceType: string;
  operatingCities: string;
  fleetSize: number;
  availableVehicleTypes: string;
  wheelchairService: boolean;
  childSeatService: boolean;
  businessLicenseUrl?: string;
  insuranceCompany: string;
  insurancePolicyNumber: string;
  insuranceExpiryDate: string;
  regulatoryCertificatesUrl?: string;
  permitExpiryDate: string;
  tenantId: any;
  status: string;
  createdAt: string;
  updatedAt: string;
  databaseName?: string;
  databaseHost: any;
  databasePort: any;
  databaseUsername: any;
  databasePassword: any;
  databaseCreated: boolean;
  tenant: any;
  permissions: PermissionType[];
  superAdmin: Omit<SuperAdminType, "PermissionType">;
}
