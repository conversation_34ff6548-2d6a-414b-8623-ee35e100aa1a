"use client";

import React, { useEffect, useState, use<PERSON><PERSON><PERSON>, <PERSON> } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useSidebar } from "@/context/SidebarContext";
import { ChevronDownIcon } from "@/icons";
import {
  PiGauge,
  PiUsersThree,
  PiSteeringWheel,
  PiCarProfile,
  PiTaxi,
  PiHeadset,
  PiCurrencyCircleDollar,
  PiGear,
  PiInfo,
  PiUsers,
  PiCar,
  PiCalendarCheck,
  PiChat,
  PiWallet,
} from "react-icons/pi";
import { TbBrandMercedes } from "react-icons/tb";
import { FiPhoneCall } from "react-icons/fi";

type Role = "super_admin" | "driver" | "customer" | "platform_admin" | "client";

interface SubItem {
  name: string;
  path: string;
  pro?: boolean;
  new?: boolean;
}

interface NavItem {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: SubItem[];
  roles?: Role[];
}

const allNavItems: NavItem[] = [
  {
    icon: <PiGauge size={20} />,
    name: "Dashboard",
    path: "/",
    roles: ["super_admin", "client", "customer"],
  },
  {
    icon: <PiCar size={20} />,
    name: "My Clients",
    path: "/super-admin/My-clients",
    roles: ["super_admin", "client"],
  },
  {
    icon: <TbBrandMercedes size={20} />,
    name: "Pricing",
    path: "/super-admin/pricing",
    roles: ["super_admin", "client"],
  },
  {
    icon: <TbBrandMercedes size={20} />,
    name: "Setting",
    path: "/super-admin/setting",
    roles: ["super_admin", "client"],
  },

  {
    icon: <PiGauge size={20} />,
    name: "Dashboard",
    path: "/admin/dashboard",
    roles: ["super_admin", "client"],
  },
  {
    icon: <PiUsers size={20} />,
    name: "Passenger",
    path: "/admin/passenger",
    roles: ["super_admin", "client"],
  },
  {
    icon: <TbBrandMercedes size={20} />,
    name: "Drivers",
    roles: ["super_admin", "client"],
    subItems: [
      { name: "All Drivers", path: "/admin/drivers" },
      { name: "Driver Documents", path: "/admin/drivers/documents" },
      { name: "Driver Payouts", path: "/admin/drivers/payouts" },
    ],
  },
  {
    icon: <PiCarProfile size={20} />,
    name: "Vehicle",
    path: "/admin/vehicle",
    roles: ["super_admin", "client"],
  },
  {
    icon: <PiUsers size={20} />,
    name: "Rides",
    path: "/admin/rides",
    roles: ["super_admin", "client"],
  },
  {
    icon: <PiHeadset size={20} />,
    name: "Incidents & Support",
    path: "/admin/incidents",
    roles: ["super_admin", "client"],
  },
  {
    icon: <PiCurrencyCircleDollar size={20} />,
    name: "Pricing",
    roles: ["super_admin", "client"],
    subItems: [
      { name: "Base Pricing", path: "/admin/pricing/base" },
      { name: "Dynamic Pricing", path: "/admin/pricing/dynamic" },
      { name: "Discounts", path: "/admin/pricing/discounts" },
    ],
  },
  {
    icon: <PiGear size={20} />,
    name: "Settings",
    roles: ["super_admin", "client"],
    subItems: [
      { name: "General", path: "/admin/settings/general" },
      { name: "Roles & Permissions", path: "/admin/settings/roles" },
      { name: "Integrations", path: "/admin/settings/integrations" },
    ],
  },
  {
    icon: <PiInfo size={20} />,
    name: "Help & Support",
    path: "/admin/help",
    roles: ["super_admin", "client"],
  },
  {
    icon: <PiTaxi size={20} />,
    name: "Rides",
    path: "/admin/rides",
  },
  {
    name: "Dashboard",
    path: "/driver/dashboard",
    icon: <PiGauge size={20} />,
    roles: ["driver"],
  },
  {
    name: "Schedule",
    path: "/driver/schedule",
    icon: <PiCalendarCheck size={20} />,
    roles: ["driver"],
  },
  {
    name: "Chat",
    path: "/driver/chat",
    icon: <PiChat size={20} />,
    roles: ["driver"],
  },
  {
    name: "Report",
    path: "/driver/report",
    icon: <PiInfo size={20} />,
    roles: ["driver"],
  },
  {
    name: "My Earnings",
    path: "/driver/my-earnings",
    icon: <PiWallet size={20} />,
    roles: ["driver"],
  },
  {
    name: "Vehicle Info",
    path: "/driver/vehicle-info",
    icon: <PiCar size={20} />,
    roles: ["driver"],
  },
];

const getNavItemsByRole = (role: Role): NavItem[] =>
  allNavItems.filter((item) => item.roles?.includes(role));

const AppSidebar: FC<{ role?: Role }> = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const pathname = usePathname();

  const [userRole, setUserRole] = useState<Role>("driver");
  const [openSubmenu, setOpenSubmenu] = useState<{
    type: "main" | "others";
    index: number;
  } | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const account = localStorage.getItem("account");
        const parsed = account ? JSON.parse(account) : null;
        if (parsed?.role) {
          setUserRole(parsed.role);
        }
      } catch (err) {
        console.error("Failed to parse account:", err);
      }
    }
  }, []);

  const navItems = getNavItemsByRole(userRole);

  const isActive = useCallback((path: string) => path === pathname, [pathname]);

  useEffect(() => {
    for (let i = 0; i < navItems.length; i++) {
      const nav = navItems[i];
      if (nav.subItems?.some((sub) => isActive(sub.path))) {
        setOpenSubmenu({ type: "main", index: i });
        return;
      }
    }
    setOpenSubmenu(null);
  }, [pathname, navItems, isActive]);

  const handleSubmenuToggle = (index: number, menuType: "main" | "others") => {
    setOpenSubmenu((prev) =>
      prev?.type === menuType && prev?.index === index
        ? null
        : { type: menuType, index },
    );
  };

  const renderMenuItems = (items: NavItem[], menuType: "main" | "others") => (
    <ul className="flex flex-col gap-3">
      {items.map((nav, idx) => {
        const isOpen =
          openSubmenu?.type === menuType && openSubmenu?.index === idx;
        const active = nav.path && isActive(nav.path);

        return (
          <li key={nav.name}>
            {nav.subItems ? (
              <button
                onClick={() => handleSubmenuToggle(idx, menuType)}
                className={`flex w-full items-center gap-4 rounded-md px-2 py-2 transition-all duration-200 ${isOpen ? "text-white" : "text-white/60 hover:text-white"} ${!isExpanded && !isHovered ? "lg:justify-center" : "lg:justify-start"}`}
              >
                <span className="text-xl">{nav.icon}</span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <>
                    <span
                      className={`font-medium ${isOpen ? "text-white" : "text-white/70"}`}
                    >
                      {nav.name}
                    </span>
                    <ChevronDownIcon
                      className={`ml-auto h-5 w-5 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
                    />
                  </>
                )}
              </button>
            ) : (
              nav.path && (
                <Link
                  href={nav.path}
                  className={`flex items-center gap-4 rounded-md px-2 py-2 transition-all duration-200 ${active ? "font-semibold text-white" : "text-white/60 hover:text-white"} ${!isExpanded && !isHovered ? "lg:justify-center" : "lg:justify-start"}`}
                >
                  <span className="text-xl">{nav.icon}</span>
                  {(isExpanded || isHovered || isMobileOpen) && (
                    <span className="font-medium">{nav.name}</span>
                  )}
                </Link>
              )
            )}
          </li>
        );
      })}
    </ul>
  );

  return (
    <aside
      className={`fixed top-0 left-0 z-40 mt-16 h-screen border-r border-gray-200 px-5 text-gray-900 transition-all duration-300 lg:mt-0 dark:border-gray-800 dark:bg-gray-900 ${isExpanded || isMobileOpen || isHovered ? "w-[236px]" : "w-[90px]"} ${isMobileOpen ? "translate-x-0" : "-translate-x-full"} lg:translate-x-0`}
      style={{
        backgroundImage: "url('/images/Side-Bar.svg')",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "bottom",
      }}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`flex py-8 ${!isExpanded && !isHovered ? "lg:justify-center" : "justify-start"}`}
      >
        <Link href="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <>
              <Image
                className="dark:hidden"
                src="/images/sidebar/logo.png"
                alt="Logo"
                width={150}
                height={40}
              />
              <Image
                className="hidden dark:block"
                src="/images/sidebar/logo.png"
                alt="Logo"
                width={150}
                height={40}
              />
            </>
          ) : (
            <Image
              src="/images/sidebar/logo-mini.png"
              alt="Logo"
              width={32}
              height={32}
            />
          )}
        </Link>
      </div>

      <div className="no-scrollbar flex flex-col overflow-y-auto">
        <nav className="mb-6">
          <div className="flex flex-col gap-4">
            {renderMenuItems(navItems, "main")}
          </div>

          <div className="py-6">
            <button className="flex w-full items-center justify-center gap-3 rounded-full bg-[#FF5F5F] px-5 py-2 text-[14px] font-normal text-white shadow">
              <span className="rounded-full bg-white p-[8px] text-[#050013]">
                <FiPhoneCall size={20} />
              </span>
              Emergency support 24/7
            </button>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default React.memo(AppSidebar);
